"use client";

import { useState, useEffect, useRef } from "react";
import { TimerDisplay, TimerDisplayRef } from "@/components/timer/timer-display";
import { TimerSettings } from "@/components/timer/timer-settings";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useFullscreenWakeLock } from "@/hooks/useFullscreenWakeLock";
import { WakeLockIndicator } from "@/components/ui/wake-lock-indicator";
import { audioCache } from "@/utils/audio-cache";
import { TimerPreset } from "@/lib/timer-presets";

const TIMER_STORAGE_KEY = "timer-duration-settings";

interface TimerDurationSettings {
  sound: string;
  backgroundColor: string;
  backgroundImage: string;
  position: { x: number; y: number };
  timerDuration: number;
  originalTimerDuration: number;
  timerStartTime: number;
  isTimerRunning: boolean;
}

interface TimerDurationClientProps {
  preset: TimerPreset;
}

// Default settings factory function
const createDefaultTimerSettings = (preset: TimerPreset): TimerDurationSettings => ({
  sound: "classic-alarm.wav",
  backgroundColor: "#1a2980",
  backgroundImage: "linear-gradient(135deg, #48c6ef 0%, #6f35c5 30%, #1a2980 60%, #2f3f87 100%)",
  position: { x: 0, y: 0 },
  timerDuration: preset.duration * 1000, // Convert seconds to milliseconds
  originalTimerDuration: preset.duration * 1000,
  timerStartTime: 0,
  isTimerRunning: false,
});

export function TimerDurationClient({ preset }: TimerDurationClientProps) {
  const [isTimerComplete, setIsTimerComplete] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const [settings, setSettings] = useState(() => createDefaultTimerSettings(preset));
  const timerDisplayRef = useRef<TimerDisplayRef | null>(null);

  // Use the custom hook for fullscreen and wake lock management
  const { isFullScreen, isWakeLockSupported, isWakeLockActive } = useFullscreenWakeLock();

  // Destructure settings for easier access
  const { sound, backgroundColor, backgroundImage, position, timerDuration, originalTimerDuration, timerStartTime, isTimerRunning } = settings;

  // Load settings from localStorage on component mount
  useEffect(() => {
    const defaultSettings = createDefaultTimerSettings(preset);
    const savedSettings = localStorage.getItem(TIMER_STORAGE_KEY);
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        // Merge saved settings with defaults, but always use preset duration
        setSettings({
          ...defaultSettings,
          ...parsed,
          timerDuration: preset.duration * 1000,
          originalTimerDuration: preset.duration * 1000,
          timerStartTime: 0,
          isTimerRunning: false,
        });
      } catch (error) {
        console.error("Failed to parse saved timer settings:", error);
        setSettings(defaultSettings);
      }
    }
    setIsLoaded(true);
  }, [preset]);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    if (isLoaded) {
      localStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(settings));
    }
  }, [settings, isLoaded]);

  // Apply background styles to html element
  useEffect(() => {
    if (!isLoaded) return;

    const htmlElement = document.documentElement;
    
    if (backgroundImage && backgroundImage !== 'none') {
      htmlElement.style.backgroundImage = backgroundImage;
    } else {
      htmlElement.style.backgroundImage = 'none';
      htmlElement.style.backgroundColor = backgroundColor;
    }

    htmlElement.style.backgroundSize = 'cover';
    htmlElement.style.backgroundPosition = 'center';
    htmlElement.style.backgroundRepeat = 'no-repeat';
    htmlElement.style.backgroundAttachment = 'fixed';
  }, [backgroundColor, backgroundImage, isLoaded]);

  // Helper functions to update individual settings
  const setSound = (newSound: string) => setSettings(prev => ({ ...prev, sound: newSound }));
  const setBackgroundColor = (newColor: string) => setSettings(prev => ({ ...prev, backgroundColor: newColor }));
  const setBackgroundImage = (newImage: string) => setSettings(prev => ({ ...prev, backgroundImage: newImage }));
  const setPosition = (newPosition: { x: number; y: number }) => setSettings(prev => ({ ...prev, position: newPosition }));
  const setTimerDuration = (newDuration: number) => setSettings(prev => ({ ...prev, timerDuration: newDuration }));
  const setOriginalTimerDuration = (newDuration: number) => setSettings(prev => ({ ...prev, originalTimerDuration: newDuration }));
  const setTimerStartTime = (newStartTime: number) => setSettings(prev => ({ ...prev, timerStartTime: newStartTime }));
  const setIsTimerRunning = (newRunning: boolean) => setSettings(prev => ({ ...prev, isTimerRunning: newRunning }));

  const handleSetTimer = (duration: number, timerSound?: string) => {
    setTimerDuration(duration);
    setOriginalTimerDuration(duration);
    setTimerStartTime(0);
    setIsTimerRunning(false);
    setIsTimerComplete(false);
    if (timerSound) {
      setSound(timerSound);
    }

    // Preload the audio for faster playback when timer completes
    const soundToPreload = timerSound || sound;
    if (soundToPreload) {
      audioCache.preload(soundToPreload).catch(error => {
        console.error("Failed to preload audio:", error);
      });
    }
  };

  const handleTimerComplete = () => {
    setIsTimerComplete(true);
    setIsTimerRunning(false);
  };

  const handleTimerStart = () => {
    setTimerStartTime(Date.now());
    setIsTimerRunning(true);
  };

  const handleTimerPause = () => {
    setIsTimerRunning(false);
    setTimerStartTime(0);
  };

  const handleTimerReset = () => {
    setTimerDuration(originalTimerDuration);
    setTimerStartTime(0);
    setIsTimerRunning(false);
    setIsTimerComplete(false);
  };

  const handleCloseDialog = () => {
    setIsTimerComplete(false);
    // Stop any playing audio
    if (timerDisplayRef.current) {
      timerDisplayRef.current.stopAudio();
    }
  };

  const handleSetNewTimer = () => {
    handleCloseDialog();
    // Reset to preset duration
    handleSetTimer(preset.duration * 1000);
  };

  if (!isLoaded) {
    return <div className="w-full h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <>
      {isFullScreen ? (
        // Full screen layout - settings button in top-right corner
        <>
          <div
            className="w-full h-screen flex items-center justify-center"
            style={{
              minHeight: '100vh',
            }}
          >
            <TimerDisplay
              ref={timerDisplayRef}
              initialTime={timerDuration}
              originalTime={originalTimerDuration}
              onComplete={handleTimerComplete}
              sound={sound}
              position={position}
              startTime={timerStartTime}
              isRunning={isTimerRunning}
              onStart={handleTimerStart}
              onPause={handleTimerPause}
              onReset={handleTimerReset}
            />
          </div>

          {/* Fullscreen settings panel - top-right */}
          <div className="fixed top-4 right-4 z-50">
            <TimerSettings
              onSetTimer={handleSetTimer}
              sound={sound}
              setSound={setSound}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
              position={position}
              setPosition={setPosition}
            />
          </div>

          {/* Wake lock indicator */}
          {isWakeLockSupported && (
            <div className="fixed bottom-4 right-4 z-40">
              <WakeLockIndicator
                isFullScreen={isFullScreen}
                isSupported={isWakeLockSupported}
                isActive={isWakeLockActive}
              />
            </div>
          )}
        </>
      ) : (
        // Normal layout - similar to main timer page
        <>
          <div
            className="w-full flex items-center justify-center relative"
            style={{
              minHeight: 'calc(100vh - 120px)', // Account for navigation
            }}
          >
            <TimerDisplay
              ref={timerDisplayRef}
              initialTime={timerDuration}
              originalTime={originalTimerDuration}
              onComplete={handleTimerComplete}
              sound={sound}
              position={position}
              startTime={timerStartTime}
              isRunning={isTimerRunning}
              onStart={handleTimerStart}
              onPause={handleTimerPause}
              onReset={handleTimerReset}
            />
          </div>

          {/* Settings panel - floating in top-right corner */}
          <div className="fixed bottom-4 right-4 z-50 md:top-20 md:bottom-auto md:right-4">
            <TimerSettings
              onSetTimer={handleSetTimer}
              sound={sound}
              setSound={setSound}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
              position={position}
              setPosition={setPosition}
            />
          </div>
        </>
      )}

      {/* Timer Complete Dialog */}
      <Dialog open={isTimerComplete} onOpenChange={setIsTimerComplete}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold text-green-600">
              🎉 Timer Complete!
            </DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-6 py-4">
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Your {preset.name.toLowerCase()} has finished!
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={handleSetNewTimer}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2"
              >
                Start New {preset.minutes}-Min Timer
              </Button>
              <Button
                onClick={handleCloseDialog}
                variant="outline"
                className="px-6 py-2"
              >
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
