import { MainLayout } from "@/components/layout/main-layout";
import { TimerDurationSEOContent } from "@/components/seo/timer-duration-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { breadcrumbConfigs } from "@/components/ui/breadcrumb";
import { TimerDurationClient } from "./timer-duration-client";
import { getTimerPresetById, getAllTimerPresets } from "@/lib/timer-presets";
import { notFound } from "next/navigation";
import { Metadata } from "next";

interface TimerDurationPageProps {
  params: Promise<{
    duration: string;
  }>;
}

// Generate static params for all timer presets
export async function generateStaticParams() {
  const presets = getAllTimerPresets();
  return presets.map((preset) => ({
    duration: preset.id,
  }));
}

// Generate metadata for SEO
export async function generateMetadata({ params }: TimerDurationPageProps): Promise<Metadata> {
  const { duration } = await params;
  const preset = getTimerPresetById(duration);

  if (!preset) {
    return {
      title: "Timer Not Found",
      description: "The requested timer duration was not found.",
    };
  }

  const title = `${preset.name} - Free Online Countdown Timer`;
  const description = `Set a ${preset.minutes}-minute timer online. ${preset.description}. Perfect for productivity, cooking, exercise, and daily activities. `;

  return {
    title,
    description,
    alternates: {
      canonical: `https://bestonlineclock.com/timer/${duration}`,
    },
  };
}

export default async function TimerDurationPage({ params }: TimerDurationPageProps) {
  const { duration } = await params;
  const preset = getTimerPresetById(duration);

  if (!preset) {
    notFound();
  }

  const breadcrumbItems = breadcrumbConfigs.timerDuration(preset.name);

  return (
    <MainLayout>
      <StructuredData
        type="timer-duration"
        timerName={preset.name}
        timerDuration={preset.duration}
        timerMinutes={preset.minutes}
        description={preset.description}
      />

      {/* Full-height timer section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)', overflow: 'hidden' }}>
        <TimerDurationClient preset={preset} />
      </section>

      {/* SEO content section - below the fold, always rendered for SEO */}
      <section className="relative z-9" data-seo-content>
        <TimerDurationSEOContent preset={preset} breadcrumbItems={breadcrumbItems} />
      </section>
    </MainLayout>
  );
}
