import Link from 'next/link';
import { Footer } from './Footer';
import { TimerPreset, getRelatedTimerPresets, getPopularTimerPresets } from '@/lib/timer-presets';

interface TimerDurationSEOContentProps {
  preset: TimerPreset;
  breadcrumbItems?: Array<{
    label: string;
    href?: string;
    icon?: React.ReactNode;
  }>;
}

export function TimerDurationSEOContent({ preset, breadcrumbItems }: TimerDurationSEOContentProps) {
  const relatedPresets = getRelatedTimerPresets(preset.id, 4);
  const popularPresets = getPopularTimerPresets();

  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Breadcrumb Navigation */}
        {breadcrumbItems && (
          <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            {breadcrumbItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <span className="mx-2">/</span>}
                {item.icon && <span className="mr-1">{item.icon}</span>}
                {item.href ? (
                  <Link href={item.href} className="hover:text-blue-600 dark:hover:text-blue-400">
                    {item.label}
                  </Link>
                ) : (
                  <span className="text-gray-900 dark:text-gray-100">{item.label}</span>
                )}
              </div>
            ))}
          </nav>
        )}

        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            {preset.name} – Free Online Countdown Timer
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            {preset.description}. Set a precise {preset.minutes}-minute countdown timer with custom alerts, 
            fullscreen mode, and no sleep interruption. Perfect for productivity, cooking, exercise, and daily activities.
          </p>
        </section>

        {/* What is this timer for */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            What is a {preset.name} Used For?
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-blue-900 dark:text-blue-100">
                🎯 Common Use Cases
              </h3>
              <ul className="space-y-2 text-blue-800 dark:text-blue-200">
                {preset.useCases.map((useCase, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span>{useCase}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-green-900 dark:text-green-100">
                💡 Perfect Scenarios
              </h3>
              <ul className="space-y-2 text-green-800 dark:text-green-200">
                {preset.scenarios.map((scenario, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    <span>{scenario}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* How to Use Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            How to Use the {preset.name}
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
              <ol className="space-y-4 text-gray-700 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">1</span>
                  <div>
                    <strong>Instant Start:</strong> The {preset.minutes}-minute timer is already set and ready to go.
                    Simply click the &quot;Start&quot; button to begin your countdown.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">2</span>
                  <div>
                    <strong>Customize Settings:</strong> Click the settings panel to choose your preferred notification sound, 
                    background, and display options.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">3</span>
                  <div>
                    <strong>Fullscreen Mode:</strong> Use fullscreen mode for distraction-free timing with sleep prevention 
                    to ensure your timer never stops.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">4</span>
                  <div>
                    <strong>Control Your Timer:</strong> Pause, resume, or reset your timer as needed. 
                    The timer will alert you with sound when the {preset.minutes} minutes are up.
                  </div>
                </li>
              </ol>
            </div>
          </div>
        </section>

        {/* Related Timers */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Other Timer Durations
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {relatedPresets.map((relatedPreset) => (
              <Link
                key={relatedPreset.id}
                href={`/timer/${relatedPreset.id}`}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 text-center"
              >
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {relatedPreset.minutes}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {relatedPreset.minutes} Minute Timer
                </div>
              </Link>
            ))}
          </div>
          <div className="text-center">
            <Link
              href="/timer"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              View All Timer Options
            </Link>
          </div>
        </section>

        {/* Popular Timers */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Most Popular Online Timers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {popularPresets.map((popularPreset) => (
              <Link
                key={popularPreset.id}
                href={`/timer/${popularPreset.id}`}
                className={`p-4 rounded-lg text-center transition-all ${
                  popularPreset.id === preset.id
                    ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500'
                    : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <div className="text-lg font-bold text-gray-900 dark:text-white mb-1">
                  {popularPreset.minutes} min
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {popularPreset.name}
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            More Time Management Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/time"
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              World Time Clock
            </Link>
            <Link
              href="/alarm"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Alarm Clock
            </Link>
            <Link
              href="/stopwatch"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Stopwatch
            </Link>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}
