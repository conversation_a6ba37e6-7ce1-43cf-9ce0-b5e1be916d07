import timerPresetsData from '@/data/timer-presets.json';

export interface TimerPreset {
  id: string;
  name: string;
  duration: number; // in seconds
  minutes: number;
  description: string;
  useCases: string[];
  scenarios: string[];
}

export function getAllTimerPresets(): TimerPreset[] {
  return timerPresetsData.presets;
}

export function getTimerPresetById(id: string): TimerPreset | null {
  return timerPresetsData.presets.find(preset => preset.id === id) || null;
}

export function getTimerPresetByMinutes(minutes: number): TimerPreset | null {
  return timerPresetsData.presets.find(preset => preset.minutes === minutes) || null;
}

export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `0:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

export function formatDurationText(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0 && minutes > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
}

export function getRelatedTimerPresets(currentId: string, limit: number = 5): TimerPreset[] {
  const allPresets = getAllTimerPresets();
  const currentPreset = getTimerPresetById(currentId);
  
  if (!currentPreset) {
    return allPresets.slice(0, limit);
  }

  // Get presets with similar durations (within 30 minutes)
  const related = allPresets
    .filter(preset => preset.id !== currentId)
    .sort((a, b) => {
      const diffA = Math.abs(a.duration - currentPreset.duration);
      const diffB = Math.abs(b.duration - currentPreset.duration);
      return diffA - diffB;
    })
    .slice(0, limit);

  return related;
}

export function getPopularTimerPresets(): TimerPreset[] {
  // Return most commonly used timer durations
  const popularIds = ['5-minute', '10-minute', '15-minute', '25-minute', '30-minute'];
  return popularIds
    .map(id => getTimerPresetById(id))
    .filter((preset): preset is TimerPreset => preset !== null);
}
